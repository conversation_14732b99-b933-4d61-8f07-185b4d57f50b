#!/bin/bash

API_URL="http://localhost:8000/api/v1"
API_KEY="crate2025"
EMAIL="<EMAIL>"
PASSWORD="<EMAIL>"
USERNAME="<EMAIL>"

# NOTE: User registration is now handled through MSAL authentication
# The /api/v1/user/create endpoint has been deprecated
echo "NOTE: User registration is now handled through MSAL authentication in the iOS app"
echo "Skipping user creation step..."

echo "Logging in..."
TOKEN=$(curl -s -X POST \
  "$API_URL/user/login" \
  -H 'accept: text/plain' \
  -H "ApiKey: $API_KEY" \
  -H 'Content-Type: application/json' \
  -d '{"email": "'$EMAIL'", "password": "'$PASSWORD'"}' | jq -r '.token')

echo "Got token: $TOKEN"

echo "Getting user info..."
curl -s -X 'GET' \
  "$API_URL/user" \
  -H 'accept: text/plain' \
  -H "Authorization: Bearer $TOKEN" \
  -H "ApiKey: $API_KEY" | jq

echo "Unfurling URL..."
curl -s -X 'POST' \
  "$API_URL/unfurl" \
  -H 'accept: text/plain' \
  -H "Authorization: Bearer $TOKEN" \
  -H "ApiKey: $API_KEY" \
  -H 'Content-Type: application/json' \
  -d '{"url": "https://open.spotify.com/track/7eFyUcVQSGSuJlxkH8xeIj?si=825275ddcef24988"}' | jq

echo "Creating collection..."
curl -s -X 'POST' \
  "$API_URL/collection" \
  -H 'accept: text/plain' \
  -H "Authorization: Bearer $TOKEN" \
  -H "ApiKey: $API_KEY" \
  -H 'Content-Type: application/json' \
  -d '{ "name": "Example", "thumbnail": "none.png" }' | jq

echo "Adding track to collection..."
curl -s -X 'POST' \
  "$API_URL/collection/1/add" \
  -H 'accept: text/plain' \
  -H "Authorization: Bearer $TOKEN" \
  -H "ApiKey: $API_KEY" \
  -H 'Content-Type: application/json' \
  -d '[1]' | jq

echo "Listing collections..."
curl -s -X 'GET' \
  "$API_URL/collection?size=20" \
  -H 'accept: text/plain' \
  -H "Authorization: Bearer $TOKEN" \
  -H "ApiKey: $API_KEY" | jq
