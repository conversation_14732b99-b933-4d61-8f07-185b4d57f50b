#!/bin/bash

# API settings
API_URL="http://localhost:8000/api/v1"
API_KEY="crate2025"
EMAIL="<EMAIL>"
PASSWORD="<EMAIL>"
USERNAME="<EMAIL>"

# Colors for better readability
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to print a separator
print_separator() {
    echo -e "\n${YELLOW}=========================================${NC}\n"
}

# Function to make API calls
call_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local auth_header=$4

    echo -e "${BLUE}Calling ${method} ${endpoint}${NC}"

    if [ -n "$data" ]; then
        if [ -n "$auth_header" ]; then
            response=$(curl -s -X "$method" "$API_URL$endpoint" \
                -H "Content-Type: application/json" \
                -H "ApiKey: $API_KEY" \
                -H "Authorization: $auth_header" \
                -d "$data")
        else
            response=$(curl -s -X "$method" "$API_URL$endpoint" \
                -H "Content-Type: application/json" \
                -H "ApiKey: $API_KEY" \
                -d "$data")
        fi
    else
        if [ -n "$auth_header" ]; then
            response=$(curl -s -X "$method" "$API_URL$endpoint" \
                -H "ApiKey: $API_KEY" \
                -H "Authorization: $auth_header")
        else
            response=$(curl -s -X "$method" "$API_URL$endpoint" \
                -H "ApiKey: $API_KEY")
        fi
    fi

    echo -e "${GREEN}Response:${NC}"
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
    echo ""

    return 0
}

# Main menu
show_main_menu() {
    print_separator
    echo -e "${BLUE}=== Crate API Testing Tool ===${NC}"
    echo "1. User Endpoints"
    echo "2. Track Endpoints"
    echo "3. Collection Endpoints"
    echo "4. Unfurl Endpoints"
    echo "5. Custom Endpoint Test"
    echo "6. Clear Screen"
    echo "0. Exit"
    echo -n "Choose a category: "
}

# User endpoints menu
show_user_menu() {
    print_separator
    echo -e "${YELLOW}=== User Endpoints ===${NC}"
    echo "1. Login (/api/v1/user/login)"
    echo "2. Create User (/api/v1/user/create)"
    echo "3. Get User Info (/api/v1/user)"
    echo "4. Refresh Token (/api/v1/user/refresh)"
    echo "0. Back to main menu"
    echo -n "Choose an option: "
}

# Track endpoints menu
show_track_menu() {
    print_separator
    echo -e "${YELLOW}=== Track Endpoints ===${NC}"
    echo "1. Get Trending Tracks (/api/v1/track/trending)"
    echo "2. Get Recent Tracks (/api/v1/track/recent)"
    echo "3. Add Track (/api/v1/track)"
    echo "4. Delete All User Tracks (/api/v1/track)"
    echo "5. Delete Track by ID (/api/v1/track/{id})"
    echo "0. Back to main menu"
    echo -n "Choose an option: "
}

# Collection endpoints menu
show_collection_menu() {
    print_separator
    echo -e "${YELLOW}=== Collection Endpoints ===${NC}"
    echo "1. Get All Collections (/api/v1/collection)"
    echo "2. Create Collection (/api/v1/collection)"
    echo "3. Add Tracks to Collection (/api/v1/collection/{id}/add)"
    echo "4. Remove Tracks from Collection (/api/v1/collection/{id}/remove)"
    echo "5. Delete Collection (/api/v1/collection/{id})"
    echo "0. Back to main menu"
    echo -n "Choose an option: "
}

# Unfurl endpoints menu
show_unfurl_menu() {
    print_separator
    echo -e "${YELLOW}=== Unfurl Endpoints ===${NC}"
    echo "1. Unfurl URL (/api/v1/unfurl)"
    echo "2. Anonymous Unfurl (/api/v1/unfurl/anonymous)"
    echo "0. Back to main menu"
    echo -n "Choose an option: "
}

# Initialize token variable
TOKEN=""

# Handle User endpoints
handle_user_endpoints() {
    while true; do
        show_user_menu
        read -r option

        case $option in
            1)  # Login
                echo "Enter email (default: $EMAIL):"
                read -r input_email
                [ -z "$input_email" ] && input_email=$EMAIL

                echo "Enter password (default: $PASSWORD):"
                read -r input_password
                [ -z "$input_password" ] && input_password=$PASSWORD

                login_data="{\"email\":\"$input_email\",\"password\":\"$input_password\"}"
                echo -e "${BLUE}Request Body:${NC}"
                echo "$login_data" | jq '.' 2>/dev/null || echo "$login_data"

                response=$(curl -s -X "POST" "$API_URL/user/login" \
                    -H "Content-Type: application/json" \
                    -H "ApiKey: $API_KEY" \
                    -d "$login_data")

                echo -e "${GREEN}Response:${NC}"
                echo "$response" | jq '.' 2>/dev/null || echo "$response"

                # Extract token (adjust based on your API response structure)
                TOKEN=$(echo "$response" | jq -r '.token' 2>/dev/null)
                USER_ID=$(echo "$response" | jq -r '.userId' 2>/dev/null)

                if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
                    echo -e "${GREEN}Login successful!${NC}"
                    echo -e "User ID: $USER_ID"
                    echo -e "Token: ${TOKEN:0:20}... (truncated)"
                else
                    echo -e "${RED}Login failed!${NC}"
                fi
                ;;

            2)  # Create User (DEPRECATED)
                echo -e "${RED}NOTE: User registration is now handled through MSAL authentication in the iOS app${NC}"
                echo -e "${RED}The /api/v1/user/create endpoint has been deprecated${NC}"
                echo -e "${YELLOW}Please use the iOS app to register new users${NC}"
                ;;

            3)  # Get User Info
                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    response=$(curl -s -X "GET" "$API_URL/user" \
                    -H "ApiKey: $API_KEY" \
                    -H "Authorization: Bearer $TOKEN")

                    echo -e "${GREEN}Response:${NC}"
                    echo "$response" | jq '.' 2>/dev/null || echo "$response"
                fi
                ;;

            4)  # Refresh Token
                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    refresh_data="{\"token\":\"$TOKEN\"}"
                    echo -e "${BLUE}Request Body:${NC}"
                    echo "$refresh_data" | jq '.' 2>/dev/null || echo "$refresh_data"

                    response=$(curl -s -X "POST" "$API_URL/user/refresh" \
                        -H "Content-Type: application/json" \
                        -H "ApiKey: $API_KEY" \
                        -d "$refresh_data")

                    echo -e "${GREEN}Response:${NC}"
                    echo "$response" | jq '.' 2>/dev/null || echo "$response"

                    # Update token if present in response
                    NEW_TOKEN=$(echo "$response" | jq -r '.token' 2>/dev/null)

                    if [ "$NEW_TOKEN" != "null" ] && [ -n "$NEW_TOKEN" ]; then
                        TOKEN=$NEW_TOKEN
                        echo -e "${GREEN}Token refreshed successfully!${NC}"
                        echo -e "New Token: ${TOKEN:0:20}... (truncated)"
                    else
                        echo -e "${YELLOW}No new token returned, keeping existing token.${NC}"
                    fi
                fi
                ;;

            0)  # Back to main menu
                return
                ;;

            *)  # Invalid option
                echo -e "${RED}Invalid option!${NC}"
                ;;
        esac

        echo "Press Enter to continue..."
        read -r
    done
}

# Handle Track endpoints
handle_track_endpoints() {
    while true; do
        show_track_menu
        read -r option

        case $option in
            1)  # Get Trending Tracks
                echo "Enter start page (default: 0):"
                read -r start_page
                [ -z "$start_page" ] && start_page=0

                echo "Enter page size (default: 20):"
                read -r page_size
                [ -z "$page_size" ] && page_size=20

                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    call_api "GET" "/track/trending?start=$start_page&size=$page_size" "" "Bearer $TOKEN"
                fi
                ;;

            2)  # Get Recent Tracks
                echo "Enter start page (default: 0):"
                read -r start_page
                [ -z "$start_page" ] && start_page=0

                echo "Enter page size (default: 20):"
                read -r page_size
                [ -z "$page_size" ] && page_size=20

                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    call_api "GET" "/track/recent?start=$start_page&size=$page_size" "" "Bearer $TOKEN"
                fi
                ;;

            3)  # Add Track
                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    echo "Enter track title:"
                    read -r track_title

                    echo "Enter artist name:"
                    read -r artist_name

                    echo "Enter media URL:"
                    read -r media_url

                    echo "Enter domain URL:"
                    read -r domain_url

                    track_data="{\"trackTitle\":\"$track_title\",\"artistName\":\"$artist_name\",\"mediaUrl\":\"$media_url\",\"domainUrl\":\"$domain_url\"}"
                    echo -e "${BLUE}Request Body:${NC}"
                    echo "$track_data" | jq '.' 2>/dev/null || echo "$track_data"

                    call_api "POST" "/track" "$track_data" "Bearer $TOKEN"
                fi
                ;;

            4)  # Delete All User Tracks
                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    echo -e "${RED}This will delete ALL your tracks. Are you sure? (y/n)${NC}"
                    read -r confirm

                    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                        call_api "DELETE" "/track" "" "Bearer $TOKEN"

                        # Check response status (204 is success)
                        if [ -z "$response" ]; then
                            echo -e "${GREEN}All tracks deleted successfully!${NC}"
                        else
                            echo -e "${RED}Failed to delete tracks!${NC}"
                        fi
                    else
                        echo "Operation cancelled."
                    fi
                fi
                ;;

            5)  # Delete Track by ID
                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    echo "Enter track ID:"
                    read -r track_id

                    call_api "DELETE" "/track/$track_id" "" "Bearer $TOKEN"

                    # Check response status (204 is success)
                    if [ -z "$response" ]; then
                        echo -e "${GREEN}Track deleted successfully!${NC}"
                    else
                        echo -e "${RED}Failed to delete track!${NC}"
                    fi
                fi
                ;;

            0)  # Back to main menu
                return
                ;;

            *)  # Invalid option
                echo -e "${RED}Invalid option!${NC}"
                ;;
        esac

        echo "Press Enter to continue..."
        read -r
    done
}

# Handle Collection endpoints
handle_collection_endpoints() {
    while true; do
        show_collection_menu
        read -r option

        case $option in
            1)  # Get All Collections
                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    echo "Enter start page (default: 0):"
                    read -r start_page
                    [ -z "$start_page" ] && start_page=0

                    echo "Enter page size (default: 20):"
                    read -r page_size
                    [ -z "$page_size" ] && page_size=20

                    call_api "GET" "/collection?start=$start_page&size=$page_size" "" "Bearer $TOKEN"
                fi
                ;;

            2)  # Create Collection
                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    echo "Enter collection name:"
                    read -r collection_name

                    echo "Enter thumbnail URL (optional):"
                    read -r thumbnail_url

                    if [ -z "$thumbnail_url" ]; then
                        collection_data="{\"name\":\"$collection_name\"}"
                    else
                        collection_data="{\"name\":\"$collection_name\",\"thumbnail\":\"$thumbnail_url\"}"
                    fi

                    echo -e "${BLUE}Request Body:${NC}"
                    echo "$collection_data" | jq '.' 2>/dev/null || echo "$collection_data"

                    call_api "POST" "/collection" "$collection_data" "Bearer $TOKEN"
                fi
                ;;

            3)  # Add Tracks to Collection
                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    echo "Enter collection ID:"
                    read -r collection_id

                    echo "Enter track IDs (comma-separated, e.g. 1,2,3):"
                    read -r track_ids

                    # Convert comma-separated list to JSON array
                    IFS=',' read -ra TRACK_ARRAY <<< "$track_ids"
                    track_array_json="["
                    for i in "${TRACK_ARRAY[@]}"; do
                        track_array_json+="$i,"
                    done
                    track_array_json=${track_array_json%,}  # Remove trailing comma
                    track_array_json+="]"

                    echo -e "${BLUE}Request Body:${NC}"
                    echo "$track_array_json" | jq '.' 2>/dev/null || echo "$track_array_json"

                    call_api "POST" "/collection/$collection_id/add" "$track_array_json" "Bearer $TOKEN"
                fi
                ;;

            4)  # Remove Tracks from Collection
                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    echo "Enter collection ID:"
                    read -r collection_id

                    echo "Enter track IDs to remove (comma-separated, e.g. 1,2,3):"
                    read -r track_ids

                    # Convert comma-separated list to JSON array
                    IFS=',' read -ra TRACK_ARRAY <<< "$track_ids"
                    track_array_json="["
                    for i in "${TRACK_ARRAY[@]}"; do
                        track_array_json+="$i,"
                    done
                    track_array_json=${track_array_json%,}  # Remove trailing comma
                    track_array_json+="]"

                    echo -e "${BLUE}Request Body:${NC}"
                    echo "$track_array_json" | jq '.' 2>/dev/null || echo "$track_array_json"

                    call_api "POST" "/collection/$collection_id/remove" "$track_array_json" "Bearer $TOKEN"
                fi
                ;;

            5)  # Delete Collection
                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    echo "Enter collection ID:"
                    read -r collection_id

                    echo -e "${RED}This will delete the collection. Are you sure? (y/n)${NC}"
                    read -r confirm

                    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                        call_api "DELETE" "/collection/$collection_id" "" "Bearer $TOKEN"

                        # Check response status (204 is success)
                        if [ -z "$response" ]; then
                            echo -e "${GREEN}Collection deleted successfully!${NC}"
                        else
                            echo -e "${RED}Failed to delete collection!${NC}"
                        fi
                    else
                        echo "Operation cancelled."
                    fi
                fi
                ;;

            0)  # Back to main menu
                return
                ;;

            *)  # Invalid option
                echo -e "${RED}Invalid option!${NC}"
                ;;
        esac

        echo "Press Enter to continue..."
        read -r
    done
}

# Handle Unfurl endpoints
handle_unfurl_endpoints() {
    while true; do
        show_unfurl_menu
        read -r option

        case $option in
            1)  # Unfurl URL
                if [ -z "$TOKEN" ]; then
                    echo -e "${RED}Please login first!${NC}"
                else
                    echo "Enter URL to unfurl:"
                    read -r track_url

                    url_data="{\"url\":\"$track_url\"}"
                    echo -e "${BLUE}Request Body:${NC}"
                    echo "$url_data" | jq '.' 2>/dev/null || echo "$url_data"

                    response=$(curl -s -X "POST" "$API_URL/unfurl" \
                        -H "Content-Type: application/json" \
                        -H "ApiKey: $API_KEY" \
                        -H "Authorization: Bearer $TOKEN" \
                        -d "$url_data")

                    echo -e "${GREEN}Response:${NC}"
                    echo "$response" | jq '.' 2>/dev/null || echo "$response"

                    # Extract and display track info if successful
                    TRACK_ID=$(echo "$response" | jq -r '.id' 2>/dev/null)
                    if [ "$TRACK_ID" != "null" ] && [ "$TRACK_ID" != "" ]; then
                        echo -e "${GREEN}Track unfurled successfully!${NC}"
                        ARTIST=$(echo "$response" | jq -r '.artist' 2>/dev/null)
                        TITLE=$(echo "$response" | jq -r '.title' 2>/dev/null)
                        echo -e "Artist: $ARTIST"
                        echo -e "Title: $TITLE"
                    else
                        echo -e "${RED}Unfurl failed!${NC}"
                    fi
                fi
                ;;

            2)  # Anonymous Unfurl
                echo "Enter URL to unfurl anonymously:"
                read -r track_url

                url_data="{\"url\":\"$track_url\"}"
                echo -e "${BLUE}Request Body:${NC}"
                echo "$url_data" | jq '.' 2>/dev/null || echo "$url_data"

                response=$(curl -s -X "POST" "$API_URL/unfurl/anonymous" \
                    -H "Content-Type: application/json" \
                    -H "ApiKey: $API_KEY" \
                    -d "$url_data")

                echo -e "${GREEN}Response:${NC}"
                echo "$response" | jq '.' 2>/dev/null || echo "$response"

                # Extract and display track info if successful
                TRACK_ID=$(echo "$response" | jq -r '.id' 2>/dev/null)
                if [ "$TRACK_ID" != "null" ] && [ "$TRACK_ID" != "" ]; then
                    echo -e "${GREEN}Track unfurled successfully!${NC}"
                    ARTIST=$(echo "$response" | jq -r '.artist' 2>/dev/null)
                    TITLE=$(echo "$response" | jq -r '.title' 2>/dev/null)
                    echo -e "Artist: $ARTIST"
                    echo -e "Title: $TITLE"
                else
                    echo -e "${RED}Unfurl failed!${NC}"
                fi
                ;;

            0)  # Back to main menu
                return
                ;;

            *)  # Invalid option
                echo -e "${RED}Invalid option!${NC}"
                ;;
        esac

        echo "Press Enter to continue..."
        read -r
    done
}

# Handle custom endpoint testing
handle_custom_endpoint() {
    echo "Enter HTTP method (GET, POST, PUT, DELETE):"
    read -r custom_method

    echo "Enter endpoint (starting with /):"
    read -r custom_endpoint

    echo "Enter request body (JSON format) or leave empty:"
    read -r custom_data

    echo "Use authentication? (y/n):"
    read -r use_auth

    auth_header=""
    if [ "$use_auth" = "y" ] || [ "$use_auth" = "Y" ]; then
        if [ -z "$TOKEN" ]; then
            echo -e "${RED}No auth token available. Please login first!${NC}"
            return
        fi
        auth_header="Bearer $TOKEN"
    fi

    call_api "$custom_method" "$custom_endpoint" "$custom_data" "$auth_header"
}

# Main loop
while true; do
    show_main_menu
    read -r option

    case $option in
        1)  # User Endpoints
            handle_user_endpoints
            ;;

        2)  # Track Endpoints
            handle_track_endpoints
            ;;

        3)  # Collection Endpoints
            handle_collection_endpoints
            ;;

        4)  # Unfurl Endpoints
            handle_unfurl_endpoints
            ;;

        5)  # Custom Endpoint Test
            handle_custom_endpoint
            ;;

        6)  # Clear Screen
            clear
            ;;

        0)  # Exit
            echo "Exiting..."
            exit 0
            ;;

        *)  # Invalid option
            echo -e "${RED}Invalid option!${NC}"
            ;;
    esac

    echo "Press Enter to continue..."
    read -r
done
