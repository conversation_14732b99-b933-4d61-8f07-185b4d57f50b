import CrateServices
import Factory
import Foundation
import SwiftData
import SwiftUI

@MainActor
final class AddUrlToCollectionViewModel: ObservableObject {
  private let contentService: ContentServiceProtocol
  private let collectionService: CollectionServiceProtocol
  private let userState: UserState
  private let modelContext: ModelContext
  private let collection: Collection
  private var debounceWorkItem: DispatchWorkItem?

  enum State {
    case empty
    case fetching
    case loaded(ContentDTO)
    case error
  }

  struct ViewState {
    var enteredURL: String = ""
    var currentURL: String = ""
    var imageURL: URL?
  }

  struct ActiveFlags: OptionSet {
    let rawValue: Int

    static let showEmptyUrlAlert = ActiveFlags(rawValue: 1 << 0)
  }

  @Published var state: State = .empty
  @Published var viewState = ViewState()
  @Published var activeFlags: ActiveFlags = []

  init(modelContext: ModelContext, collection: Collection) {
    self.modelContext = modelContext
    self.collection = collection
    self.contentService = Container.shared.contentService.resolve()
    self.collectionService = Container.shared.collectionService.resolve()
    self.userState = Container.shared.userState.resolve()
  }

  func clearContentInfo() {
    viewState.enteredURL = ""
    viewState.currentURL = ""
    viewState.imageURL = nil
    state = .empty
  }

  func addContentToCollection() async throws {
    guard case .loaded(let contentDTO) = state else { return }

    let contentModel = contentDTO.toModel()
    collection.content.append(contentModel)
    try modelContext.save()
  }

  func handleURLChange() {
    debounceWorkItem?.cancel()  // Cancel any existing debounce task

    let workItem = DispatchWorkItem { [weak self] in
      guard let self = self else { return }
      guard !self.viewState.currentURL.isEmpty else {
        self.clearContentInfo()
        return
      }

      Task {
        self.state = .fetching

        do {
          try await self.contentService.unfurl(url: self.viewState.currentURL)
          if let contentData = try await self.contentService.getSingleRecent() {
            self.state = .loaded(contentData)
            if let mediaUrl = contentData.mediaUrl {
              self.viewState.imageURL = URL(string: mediaUrl)
            }
          } else {
            self.state = .error
          }
        } catch {
          print("Error unfurling URL: \(error)")
          self.state = .error
        }
      }
    }

    debounceWorkItem = workItem
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: workItem)  // 300ms debounce
  }

  func handleAddToCollection() {
    guard case let .loaded(contentDTO) = state else { return }

    Task {
      do {
        let content = contentDTO.toModel()
        try contentService.saveTrendingContent([content], context: modelContext)

        // Add content to collection locally
        collection.content.append(content)
        try modelContext.save()

        // Sync with server if user is signed in
        if userState.isSignedIn,
          let collectionServerId = collection.serverId,
          let contentServerId = content.serverId
        {
          let success = try await collectionService.addContentToCollection(
            collectionServerId: collectionServerId,
            contentServerId: contentServerId
          )

          if !success {
            // If server sync fails, remove the content locally
            collection.content.removeAll(where: { $0 == content })
            try modelContext.save()
            print("Failed to add content to collection on server")
          }
        }
      } catch {
        print("Error saving content: \(error)")
      }
    }
  }
}
