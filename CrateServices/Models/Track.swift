import Foundation
import SwiftData

@Model
public final class Track {
  public var serverId: Int?
  public var name: String?
  public var artist: String?
  public var imgUrl: String?
  public var url: String?
  public var domain: String?
  public var recentCollections: RecentCollection?
  public var collections: [Collection]
  public var updatedAt: Date?
  public var createdAt: Date?

  public init(
    serverId: Int? = nil,
    name: String? = nil,
    artist: String? = nil,
    imgUrl: String? = nil,
    url: String? = nil,
    domain: String? = nil,
    recentCollections: RecentCollection? = nil,
    collections: [Collection] = [],
    updatedAt: Date? = nil,
    createdAt: Date? = nil
  ) {
    self.serverId = serverId
    self.name = name
    self.artist = artist
    self.imgUrl = imgUrl
    self.url = url
    self.domain = domain
    self.recentCollections = recentCollections
    self.collections = collections
    self.updatedAt = updatedAt
    self.createdAt = createdAt
  }

  public func toDTO() -> TrackDTO {
    TrackDTO(
      serverId: serverId,
      name: name,
      artist: artist,
      imgUrl: imgUrl,
      url: url,
      domain: domain,
      recentCollections: nil,
      collections: nil,
      updatedAt: updatedAt,
      createdAt: createdAt
    )
  }
}

public final class TrackDTO: Sendable, Identifiable {
  public let serverId: Int?
  public let name: String?
  public let artist: String?
  public let imgUrl: String?
  public let url: String?
  public let domain: String?
  public let recentCollections: RecentCollectionDTO?
  public let collections: [CollectionDTO]?
  public let updatedAt: Date?
  public let createdAt: Date?

  public init(
    serverId: Int? = nil,
    name: String? = nil,
    artist: String? = nil,
    imgUrl: String? = nil,
    url: String? = nil,
    domain: String? = nil,
    recentCollections: RecentCollectionDTO? = nil,
    collections: [CollectionDTO]? = nil,
    updatedAt: Date? = nil,
    createdAt: Date? = nil
  ) {
    self.serverId = serverId
    self.name = name
    self.artist = artist
    self.imgUrl = imgUrl
    self.url = url
    self.domain = domain
    self.recentCollections = recentCollections
    self.collections = collections
    self.updatedAt = updatedAt
    self.createdAt = createdAt
  }

  public func toModel() -> Track {
    Track(
      serverId: serverId,
      name: name,
      artist: artist,
      imgUrl: imgUrl,
      url: url,
      domain: domain,
      recentCollections: nil,
      collections: [],
      updatedAt: updatedAt,
      createdAt: createdAt
    )
  }
}
